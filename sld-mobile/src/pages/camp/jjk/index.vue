<template>
  <view class="camp-page" :style="mix_diyStyle">
    <!-- 页面背景 -->
    <view class="page-background">
      <image
        class="bg-image"
        src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_bg.png"
        mode="aspectFill"
      />
    </view>

    <!-- 粒子效果背景 -->
    <canvas
      id="particleCanvas"
      canvas-id="particleCanvas"
      class="particle-canvas"
    ></canvas>

    <view class="container">
      <!-- 卡片区域 -->
      <view class="cards-container">
        <!-- 左侧卡片 -->
        <view class="side-card left-card">
          <image
            class="side-card-image"
            src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_side_bg.png"
            mode="aspectFit"
          />
        </view>

        <!-- 中间主卡片 -->
        <view class="main-card" :class="{ 'card-flip': isCardDrawn || isFlipping, 'card-flip-reset': isFlippingReset, 'holo-sweep': holoSweep, 'holo-alt': holoAlt }" @click="handleCardClick">
          <!-- 卡片背面（默认显示） -->
          <view class="card-back">
            <image
              class="main-card-image"
              :src="cardBackImage"
              mode="aspectFit"
            />
          </view>
          <!-- 卡片正面（翻转后显示） -->
          <view class="card-front">
            <image
              class="main-card-image"
              :src="drawnCardImage"
              mode="aspectFit"
              v-if="drawnCardImage"
            />
          </view>
        </view>

        <!-- 右侧卡片 -->
        <view class="side-card right-card">
          <image
            class="side-card-image"
            src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_side_bg.png"
            mode="aspectFit"
          />
        </view>
      </view>

      <!-- 抽取按钮 - 只在未抽卡时显示 -->
      <view class="draw-button-container" v-if="!isCardDrawn">
        <image
          class="draw-button"
          src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_button.png"
          mode="widthFix"
          @click="drawCard"
          :class="{ 'disabled': isDrawing }"
        />
        <view class="button-text">{{ drawButtonText }}</view>
      </view>

      <!-- 抽卡成功后的三个按钮 -->
      <view class="action-buttons-container" v-if="isCardDrawn">
        <!-- 抽取金句卡按钮 -->
        <view class="action-button-item">
          <view
            class="action-button first-button"
            @click="handleFirstButtonClick"
            :class="{ 'disabled': isDrawing }"
          >
            <view class="action-button-text">{{ firstButtonText }}</view>
          </view>
        </view>

        <!-- 入群按钮 -->
        <view class="action-button-item">
          <image
            class="action-button"
            src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_small_group_button.png"
            @click="showChatroomPopup"
            mode="widthFix"
          />
        </view>

        <!-- 分享按钮 -->
        <view class="action-button-item">
          <image
            class="action-button"
            src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_small_share_button.png"
            @click="generatePoster"
            :class="{ 'disabled': isGeneratingPoster }"
            mode="widthFix"
          />
        </view>
      </view>

      <!-- 调试信息 -->
      <!-- <view style="position: fixed; top: 100rpx; left: 20rpx; background: rgba(0,0,0,0.8); color: white; padding: 10rpx; font-size: 20rpx; z-index: 999; border-radius: 10rpx; max-width: 300rpx;" v-if="true">
        <view>isCardDrawn: {{ isCardDrawn }}</view>
        <view>drawnCardImage: {{ !!drawnCardImage }}</view>
        <view>currentRid: {{ currentRid }}</view>
        <view>cachedResourceId: {{ cachedResourceId }}</view>
        <view>isFirstVisit: {{ isFirstVisit }}</view>
        <view>activityImageUrl: {{ !!activityImageUrl }}</view>
        <view>showActivityModal: {{ showActivityModal }}</view>
        <view>hasActivity: {{ hasActivity }}</view>
        <view>drawButtonText: {{ drawButtonText }}</view>
        <view>isDrawing: {{ isDrawing }}</view>
        <view>isFlipping: {{ isFlipping }}</view>
        <view style="margin-top: 10rpx; padding-top: 10rpx; border-top: 1rpx solid #666;">
          <view>三个按钮容器显示: {{ isCardDrawn ? '是' : '否' }}</view>
          <view>activityModalClosed: {{ activityModalClosed }}</view>
          <view>firstButtonText: {{ firstButtonText }}</view>
        </view>
        <view style="margin-top: 10rpx; padding-top: 10rpx; border-top: 1rpx solid #666;">
          <view @click="clearCachedActivityResourceId" style="background: #ff4444; padding: 5rpx 10rpx; border-radius: 5rpx; text-align: center; margin-bottom: 5rpx;">清除缓存</view>
          <view @click="checkActivityStatus" style="background: #4444ff; padding: 5rpx 10rpx; border-radius: 5rpx; text-align: center; margin-bottom: 5rpx;">重新检查活动</view>
        </view>
      </view> -->

      <!-- 底部Banner区域 -->
      <!-- <view class="banner-container"></view> -->
    </view>

    <!-- 登录弹窗 -->
    <loginPop ref="loginPop"></loginPop>

    <!-- 活动弹窗 -->
    <view class="activity-modal" v-if="showActivityModal" @click="closeActivityModal">
      <view class="activity-modal-content" @click.stop>


        <image
          v-if="activityImageUrl"
          :src="activityImageUrl"
          class="activity-image"
          mode="aspectFit"
          @load="onActivityImageLoad"
          @error="onActivityImageError"
        />

        <!-- 如果没有图片，显示占位内容 -->
        <view v-else class="activity-placeholder">
          <text>暂无活动图片</text>
        </view>

        <!-- 关闭按钮移到下方 -->
        <view class="activity-close-btn-bottom" @click="closeActivityModal">
          ×
        </view>
      </view>
    </view>

    <!-- 海报分享展示 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-display" @click.stop>
        <!-- 显示 fastposter 生成的海报 -->
        <image
          v-if="posterImagePath"
          :src="posterImagePath"
          class="poster-image"
          mode="aspectFit"
        />
        <!-- 备用 canvas（如果需要的话） -->
        <canvas
          v-else
          canvas-id="posterCanvas"
          class="poster-canvas"
          :style="{ width: posterCanvasWidth + 'px', height: posterCanvasHeight + 'px' }"
        ></canvas>

        <!-- 提示文字 -->
        <view class="poster-tip">
          长按海报图片保存
        </view>

        <!-- 关闭按钮 -->
        <view class="poster-close-btn" @click="closePosterModal">
          ×
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import loginPop from '@/components/loginPop/loginPop.vue'
import fastposter from 'fastposter'

export default {
  components: {
    loginPop
  },
  data() {
    return {
      // 卡片背面固定图片
      cardBackImage: 'https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_bg.png',
      // 抽取到的卡片图片（正面）
      drawnCardImage: null,
      isDrawing: false,
      isFlipping: false,
      // 二次抽卡时临时把主卡片翻回到背面（不改变按钮状态）
      isFlippingReset: false,
      // 控制镭射扫光在每次翻正面时触发
      holoSweep: false,
      // 切换方案A/B，借由不同动画名强制重新播放
      holoAlt: false,
      // 卡片是否已经被抽取（决定显示正面还是背面）
      isCardDrawn: false,
      currentRid: null,
      groupKey: 'NzfYPDDe', // 彩虹卡资源组 - 用于CMS接口的表单组key
      // 活动检查相关字段
      activityGroupKey: 'gbkKxijV', // 活动检查组key (测试key)
      cachedResourceId: null, // 缓存的活动resourceId
      activityImageUrl: null, // 活动弹窗图片URL
      showActivityModal: false, // 活动弹窗显示状态
      isFirstVisit: true, // 是否首次访问
      activityModalClosed: false, // 活动弹窗是否被用户关闭过
      // Link 类型相关字段
      activityLinkType: null, // 活动类型 (link 或其他)
      activityLinkUrl: null, // 链接地址
      activityLinkText: null, // 链接文案
      // 原有字段
      isGeneratingPoster: false,
      showPosterModal: false,
      posterCanvasWidth: 375,
      posterCanvasHeight: 667,
      posterImagePath: '',
      resourceData: null, // 存储抽到的资源数据
      spreaderInfo: null, // 存储推广者信息
      // Canvas粒子效果数据
      canvas: null,
      ctx: null,
      particleCanvasWidth: 375,
      particleCanvasHeight: 667,
      stars: [],
      starCount: 0,
      maxStars: 100,
      hue: 45, // 金色色调
      animationId: null,
      // 仅用于本地排查Canvas的调试开关，线上必须保持false
      debugCanvas: false
    }
  },
  computed: {
    ...mapState(['hasLogin']),

    // 判断是否有活动
    hasActivity() {
      return !!this.activityImageUrl
    },

    // 抽卡按钮文案
    drawButtonText() {
      if (this.isDrawing) {
        return '抽取中...'
      }
      // 只有在抽卡后且有活动时才显示"进群获取实体卡"
      if (this.isCardDrawn && this.hasActivity) {
        return '获取实体卡'
      }
      return '抽取金句卡'
    },

    // 第一个按钮文案（三个按钮中的第一个）
    firstButtonText() {
      console.log('firstButtonText 计算中:', {
        isDrawing: this.isDrawing,
        hasActivity: this.hasActivity,
        activityModalClosed: this.activityModalClosed,
        activityLinkType: this.activityLinkType,
        activityLinkText: this.activityLinkText
      })

      if (this.isDrawing) {
        return '抽取中...'
      }
      // 如果有活动且弹窗没有被关闭过
      if (this.hasActivity && !this.activityModalClosed) {
        // 如果是 link 类型，显示自定义文案
        if (this.activityLinkType === 'link' && this.activityLinkText) {
          console.log('返回 link 类型文案:', this.activityLinkText)
          return this.activityLinkText
        }
        console.log('返回默认获取实体卡文案')
        return '获取实体卡'
      }
      console.log('返回抽取金句卡文案')
      return '抽取金句卡'
    }
  },
  onLoad(options) {
    setTimeout(() => {
      uni.setNavigationBarTitle({
        title: '潜动力金句卡'
      })
    }, 0)

    // 检查是否有rid参数
    if (options.rid) {
      this.currentRid = options.rid
      this.loadCardByRid(options.rid)
    }

    // 初始化缓存数据
    this.initCacheData()

    // 背面始终显示固定图片，不需要额外设置

    // 初始化粒子效果
    this.initParticles()
  },
  async onShow() {
    // #ifdef H5
    // H5端检查URL参数中的rid
    if (this.$Route && this.$Route.query && this.$Route.query.rid) {
      const rid = this.$Route.query.rid
      if (rid !== this.currentRid) {
        this.currentRid = rid
        this.loadCardByRid(rid)
      }
    }
    // #endif

    this.checkLogin()
    this.getSpreaderInfo()
    await this.$handleFx()

    // 检查活动状态
    this.checkActivityStatus()
  },
  onUnload() {
    // 清理动画
    this.stopAnimation()
  },
  methods: {


    // 第一个按钮点击处理
    handleFirstButtonClick() {
      console.log('第一个按钮被点击，当前状态:', {
        hasActivity: this.hasActivity,
        activityModalClosed: this.activityModalClosed,
        activityLinkType: this.activityLinkType,
        activityLinkUrl: this.activityLinkUrl
      })

      // 如果有活动且弹窗没有被关闭过
      if (this.hasActivity && !this.activityModalClosed) {
        // 如果是 link 类型，跳转链接
        if (this.activityLinkType === 'link' && this.activityLinkUrl) {
          console.log('执行 link 跳转:', this.activityLinkUrl)
          this.openExternalLink(this.activityLinkUrl)
        } else {
          // 否则显示弹窗
          console.log('显示活动弹窗')
          this.showActivityPopup()
        }
      } else {
        // 否则执行抽卡
        console.log('执行抽卡')
        this.drawCard()
      }
    },

    // 检查登录状态
    checkLogin() {
      if (!this.hasLogin) {
        this.$refs&&this.$refs.loginPop&&this.$refs.loginPop.openLogin()
      }
      return this.hasLogin
    },

    // 获取推广者信息
    getSpreaderInfo() {
      if (!this.hasLogin) return

      this.$request({
        url: '/v3/spreader/front/spreader/getInfo',
        method: 'GET'
      }).then(res => {
        if (res.state === 200 && res.data) {
          this.spreaderInfo = res.data
        } else {
          console.warn('获取推广者信息失败:', res)
        }
      }).catch(err => {
        console.error('获取推广者信息接口调用失败:', err)
      })
    },

    // 检查活动状态
    checkActivityStatus(shouldShowPopup = false) {
      if (!this.hasLogin) return

      console.log('开始检查活动状态, shouldShowPopup:', shouldShowPopup)

      // 准备请求参数
      const requestData = {
        groupKey: this.activityGroupKey
      }

      // 如果不是首次访问，添加lastResourceId参数
      if (!this.isFirstVisit && this.cachedResourceId) {
        requestData.lastResourceId = this.cachedResourceId
        console.log('活动检查，传入lastResourceId:', this.cachedResourceId)
      } else {
        console.log('首次活动检查，不传lastResourceId')
      }

      this.$request({
        url: 'v3/promotion/front/cms/get',
        method: 'GET',
        data: requestData
      }).then(res => {
        if (res.state === 200 && res.data && Array.isArray(res.data) && res.data.length > 0) {
          const activityData = res.data[0] // 取数组第一个元素
          const resourceId = activityData.resourceId || activityData.rid || activityData.id
          // 从resourceData中获取弹窗图片
          const resourceData = activityData.resourceData || {}
          const imageUrl = this.findImageUrl(resourceData) || activityData.imageUrl || activityData.image || activityData.url

          // 解析 link 类型数据
          this.parseActivityLinkData(resourceData)

          console.log('活动检查成功:', { resourceId, imageUrl, activityData, linkType: this.activityLinkType })

          if (resourceId) {
            // 检查是否是新活动
            const isNewActivity = this.cachedResourceId && resourceId !== this.cachedResourceId

            if (isNewActivity) {
              console.log('检测到新活动:', { newResourceId: resourceId, oldResourceId: this.cachedResourceId })
              // 重置活动弹窗关闭状态，让新活动可以显示弹窗
              this.activityModalClosed = false
            }

            // 更新缓存的活动resourceId
            this.cacheActivityResourceId(resourceId)
            this.cachedResourceId = resourceId
          }

          if (imageUrl) {
            this.activityImageUrl = imageUrl
            console.log('设置活动弹窗图片:', imageUrl)

            // 如果需要显示弹窗，则在设置图片后显示
            if (shouldShowPopup) {
              console.log('检查活动状态完成，准备显示弹窗')
              setTimeout(() => {
                this.showActivityModal = true
                console.log('弹窗已设置为显示状态')
              }, 500)
            }
          }

          this.isFirstVisit = false
        } else {
          console.log('无活动数据，清除活动图片URL:', res)
          // 清除活动图片URL，避免显示过期的弹窗
          this.activityImageUrl = ''
        }
      }).catch(err => {
        console.error('活动检查接口调用失败:', err)
      })
    },

    // 缓存活动resourceId
    cacheActivityResourceId(resourceId) {
      try {
        uni.setStorageSync('cached_activity_resource_id', resourceId)
        console.log('活动resourceId缓存成功:', resourceId)
      } catch (error) {
        console.error('缓存活动resourceId失败:', error)
      }
    },

    // 获取缓存的活动resourceId
    getCachedActivityResourceId() {
      try {
        const cachedId = uni.getStorageSync('cached_activity_resource_id')
        console.log('获取缓存的活动resourceId:', cachedId)
        return cachedId
      } catch (error) {
        console.error('获取缓存的活动resourceId失败:', error)
        return null
      }
    },

    // 初始化缓存数据
    initCacheData() {
      // 读取缓存的活动resourceId
      const cachedResourceId = this.getCachedActivityResourceId()
      if (cachedResourceId) {
        this.cachedResourceId = cachedResourceId
        this.isFirstVisit = false
        console.log('从缓存中恢复活动resourceId:', cachedResourceId)
      } else {
        this.isFirstVisit = true
        console.log('没有缓存的活动resourceId，标记为首次访问')
      }
    },

    // 清除缓存的活动resourceId（用于测试或重置）
    clearCachedActivityResourceId() {
      try {
        uni.removeStorageSync('cached_activity_resource_id')
        this.cachedResourceId = null
        this.isFirstVisit = true
        console.log('清除缓存的活动resourceId成功')
      } catch (error) {
        console.error('清除缓存的活动resourceId失败:', error)
      }
    },

    // 从resourceData中查找图片URL
    findImageUrl(resourceData) {
      if (!resourceData) return null

      // 查找所有可能的图片字段
      const imageFields = Object.keys(resourceData).filter(key =>
        key.includes('image_upload') ||
        key.includes('imageUrl') ||
        key.includes('image') ||
        key.includes('url')
      )

      // 返回第一个找到的图片URL
      for (const field of imageFields) {
        const url = resourceData[field]
        if (url && typeof url === 'string' && (url.startsWith('http') || url.startsWith('//'))) {
          console.log('找到图片字段:', field, '=', url)
          return url
        }
      }

      console.log('未找到有效的图片字段，resourceData:', resourceData)
      return null
    },

    // 解析活动 link 类型数据
    parseActivityLinkData(resourceData) {
      if (!resourceData) {
        this.activityLinkType = null
        this.activityLinkUrl = null
        this.activityLinkText = null
        return
      }

      // 查找 select1754711290529 字段判断类型
      const linkType = resourceData.select1754711290529
      if (linkType === 'link') {
        this.activityLinkType = 'link'
        // 获取链接地址 input1754711341812
        this.activityLinkUrl = resourceData.input1754711341812 || null
        // 获取文案 input1754710860048
        this.activityLinkText = resourceData.input1754710860048 || '点击跳转'
        console.log('解析到 link 类型活动:', {
          type: this.activityLinkType,
          url: this.activityLinkUrl,
          text: this.activityLinkText
        })
      } else {
        this.activityLinkType = null
        this.activityLinkUrl = null
        this.activityLinkText = null
        console.log('非 link 类型活动，使用默认弹窗逻辑')
      }
    },

    // 打开外部链接
    openExternalLink(url) {
      if (!url) {
        console.warn('链接地址为空')
        return
      }

      console.log('准备跳转外部链接:', url)

      // #ifdef H5
      window.open(url, '_blank')
      // #endif

      // #ifndef H5
      // 小程序端使用 skip_to 页面跳转
      uni.navigateTo({
        url: `/pages/index/skip_to?url=${encodeURIComponent(url)}`,
        fail: (err) => {
          console.error('跳转失败:', err)
          // 如果跳转失败，复制链接到剪贴板
          uni.setClipboardData({
            data: url,
            success: () => {
              uni.showModal({
                title: '提示',
                content: '链接已复制，请在浏览器中打开'
              })
            }
          })
        }
      })
      // #endif
    },

    // 处理卡片点击 - 如果有预加载的图片，可以翻转查看
    handleCardClick() {
      if (this.isDrawing) return // 抽取中不允许点击

      // 如果有预加载的图片，允许翻转查看
      if (this.drawnCardImage && this.currentRid) {
        this.isCardDrawn = !this.isCardDrawn
      }
    },

    // 初始化粒子效果
    initParticles() {
      // console.log('开始初始化Canvas粒子效果')

      // 延迟初始化，确保DOM已渲染
      this.$nextTick(() => {
        setTimeout(() => {
          this.initCanvas()
        }, 1000)
      })
    },

    // 初始化Canvas
    initCanvas() {
      console.log('initCanvas 开始执行')
      try {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync()
        this.particleCanvasWidth = systemInfo.windowWidth
        // 使用屏幕高度而不是窗口高度，确保覆盖整个屏幕包括安全区域
        this.particleCanvasHeight = systemInfo.screenHeight || systemInfo.windowHeight

        console.log('屏幕尺寸:', this.particleCanvasWidth, this.particleCanvasHeight)

        // 创建Canvas上下文
        this.ctx = uni.createCanvasContext('particleCanvas', this)
        // console.log('Canvas上下文对象:', this.ctx)

        if (this.ctx) {
          // console.log('✅ Canvas上下文创建成功')

          // 可选：仅在调试时绘制测试圆形
          if (this.debugCanvas) {
            this.drawTestCircle()
          }

          // 创建星星
          this.createStars()

          // 开始动画循环
          this.startAnimation()
        } else {
          console.error('❌ Canvas上下文创建失败')
        }
      } catch (error) {
        console.error('❌ Canvas初始化失败:', error)
      }
    },

    // 绘制测试圆形（仅调试用，默认不调用）
    drawTestCircle() {
      if (!this.ctx) return
      this.ctx.setFillStyle('#FF0000')
      this.ctx.beginPath()
      this.ctx.arc(100, 100, 30, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.draw()
    },

    // 创建星星
    createStars() {
      this.stars = []
      this.starCount = 0

      for (let i = 0; i < this.maxStars; i++) {
        this.createStar()
      }
    },

    // 创建单个星星
    createStar() {
      const star = {
        orbitRadius: this.random(this.maxOrbit(this.particleCanvasWidth, this.particleCanvasHeight)),
        radius: this.random(1, 2), // 1-2像素
        orbitX: this.particleCanvasWidth / 2,
        orbitY: this.particleCanvasHeight / 2,
        timePassed: this.random(0, this.maxStars),
        speed: 0,
        alpha: this.random(2, 10) / 10
      }

      star.speed = this.random(star.orbitRadius) / 50000

      this.starCount++
      this.stars[this.starCount] = star
    },

    // 开始动画循环
    startAnimation() {
      // console.log('开始动画循环')

      // 使用setInterval代替requestAnimationFrame，更稳定
      this.animationId = setInterval(() => {
        if (!this.ctx) return

        try {
          // 清除画布
          this.ctx.clearRect(0, 0, this.particleCanvasWidth, this.particleCanvasHeight)

          // 绘制背景
          this.ctx.setFillStyle('rgba(0, 0, 20, 0.1)')
          this.ctx.fillRect(0, 0, this.particleCanvasWidth, this.particleCanvasHeight)

          // 绘制星星
          for (let i = 1; i < this.stars.length; i++) {
            if (this.stars[i]) {
              this.drawStar(this.stars[i])
            }
          }

          this.ctx.draw()
        } catch (error) {
          console.error('动画绘制错误:', error)
        }
      }, 16) // 约60FPS
    },

    // 绘制星星
    drawStar(star) {
      const x = Math.sin(star.timePassed) * star.orbitRadius + star.orbitX
      const y = Math.cos(star.timePassed) * star.orbitRadius + star.orbitY
      const twinkle = this.random(30)

      // 闪烁效果
      if (twinkle === 1 && star.alpha > 0.2) {
        star.alpha -= 0.02
      } else if (twinkle === 2 && star.alpha < 0.8) {
        star.alpha += 0.02
      }

      // 计算当前透明度
      const alpha = Math.max(0.2, Math.min(1, star.alpha))

      // 绘制星星（简化版本）
      this.ctx.setFillStyle(`rgba(255, 215, 0, ${alpha})`)
      this.ctx.beginPath()
      this.ctx.arc(x, y, star.radius, 0, Math.PI * 2)
      this.ctx.fill()

      // 中心亮点（只有2像素的星星才有亮点）
      if (star.radius >= 2) {
        this.ctx.setFillStyle(`rgba(255, 255, 255, ${alpha * 0.8})`)
        this.ctx.beginPath()
        this.ctx.arc(x, y, star.radius * 0.4, 0, Math.PI * 2)
        this.ctx.fill()
      }

      star.timePassed += star.speed
    },

    // 工具函数：随机数
    random(min, max) {
      if (arguments.length < 2) {
        max = min
        min = 0
      }

      if (min > max) {
        const hold = max
        max = min
        min = hold
      }

      return Math.floor(Math.random() * (max - min + 1)) + min
    },

    // 工具函数：最大轨道半径
    maxOrbit(x, y) {
      const max = Math.max(x, y)
      const diameter = Math.round(Math.sqrt(max * max + max * max))
      return diameter / 2
    },

    // 创建爆发粒子效果
    createBurstParticles() {
      if (!this.ctx) return

      // console.log('创建爆发粒子效果')

      // 临时增加更多亮星
      const burstCount = 30
      const centerX = this.particleCanvasWidth / 2
      const centerY = this.particleCanvasHeight * 0.45 // 卡片位置

      for (let i = 0; i < burstCount; i++) {
        const angle = (360 / burstCount) * i
        const distance = this.random(50, 150)

        const burstStar = {
          orbitRadius: distance,
          radius: this.random(1, 2), // 爆发粒子也是1-2像素
          orbitX: centerX,
          orbitY: centerY,
          timePassed: angle * Math.PI / 180,
          speed: this.random(100, 300) / 100000,
          alpha: 1,
          isBurst: true,
          life: 60 // 60帧生命周期
        }

        this.stars.push(burstStar)
      }

      // 1秒后清理爆发粒子
      setTimeout(() => {
        this.stars = this.stars.filter(star => !star.isBurst)
        // console.log('清理爆发粒子，剩余星星数量:', this.stars.length)
      }, 1000)
    },

    // 停止动画
    stopAnimation() {
      if (this.animationId) {
        clearInterval(this.animationId)
        this.animationId = null
      }
    },

    // 根据rid加载对应卡片
    loadCardByRid(rid) {
      if (!rid) return

      // 根据技术方案，rid格式应该是 groupKey + id，比如 NzfYPDDe01
      // 这里我们先简单处理，直接根据rid构造图片URL
      // 实际项目中应该调用接口获取资源信息

      // 临时处理：如果rid是数字，则使用对应的图片
      if (/^\d+$/.test(rid)) {
        this.drawnCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${rid}.webp`
        this.currentRid = rid
        // 通过URL参数加载的卡片，显示为已抽取状态，这样分享按钮会显示
        this.isCardDrawn = true
      } else {
        // 调用新的CMS接口获取具体资源信息
        this.$request({
          url: 'v3/promotion/front/cms/get',
          method: 'GET',
          data: {
            groupKey: this.groupKey,
            rid: rid
          }
        }).then(res => {
          if (res.state === 200 && res.data && Array.isArray(res.data) && res.data.length > 0) {
            const resourceData = res.data[0]
            this.resourceData = resourceData
            // 根据新接口的数据结构调整字段访问
            const resourceDataContent = resourceData.resourceData || {}
            const imageUrl = this.findImageUrl(resourceDataContent) || resourceData.imageUrl || resourceData.image || resourceData.url

            if (imageUrl) {
              this.drawnCardImage = imageUrl
              this.currentRid = rid
              // 通过URL参数加载的卡片，显示为已抽取状态，这样分享按钮会显示
              this.isCardDrawn = true
              console.log('成功加载卡片资源:', { rid, imageUrl })
            } else {
              console.warn('CMS接口返回的数据中没有找到图片URL:', resourceData)
              this.$api.msg('卡片资源加载失败')
              this.drawnCardImage = null
              this.isCardDrawn = false
            }
          } else {
            console.warn('CMS接口返回状态异常:', res)
            this.$api.msg(res.msg || '获取资源失败')
            this.drawnCardImage = null
            this.isCardDrawn = false
          }
        }).catch(err => {
          console.error('CMS接口调用失败:', err)
          this.$api.msg('网络错误，请稍后重试')
          this.drawnCardImage = null
          this.isCardDrawn = false
        })
      }
    },
    
    // 生成海报
    generatePoster() {
      if (!this.hasLogin) {
        this.$refs&&this.$refs.loginPop&&this.$refs.loginPop.openLogin()
        return
      }

      if (this.isGeneratingPoster) {
        return
      }

      if (!this.drawnCardImage) {
        this.$api.msg('请先抽取卡片')
        return
      }

      this.isGeneratingPoster = true

      uni.showLoading({
        title: '生成海报中...'
      })

      // 确保有推广者信息
      if (!this.spreaderInfo) {
        this.getSpreaderInfo()
        setTimeout(() => {
          this.buildFastPoster()
        }, 1000)
      } else {
        this.buildFastPoster()
      }
    },

    // 使用 fastposter 生成海报
    buildFastPoster() {
      try {
        // 1. 创建海报客户端对象
        const client = fastposter.init({ token: "a70880aade1b4afe8120",b64: true,type:'jpg'})

        // 2. 准备海报参数
        const currentTime = new Date()
        const timeString = `${currentTime.getFullYear()}年${currentTime.getMonth() + 1}月${currentTime.getDate()}日`

        const shareUrl= window.location.href
        console.log('当前时间:', timeString)
        const params = {
          "camp_avatar": this.spreaderInfo?.memberAvatar || "https://store.fastposter.net/static/images/xiaoniu.png",
          "camp_image": this.drawnCardImage,
          "camp_qrcode": shareUrl, // 占位二维码
          "camp_time": timeString,
          "camp_userinfo": this.spreaderInfo?.memberNickName ? `来自${this.spreaderInfo.memberNickName}的分享` : "来自用户的分享"
        }

        console.log('海报参数:', params)

        // 3. 生成海报
        client.buildPoster('fcc22dca798d4300', params)
          .then((res) => {
            console.log('海报生成成功')
            this.posterImagePath = res
            this.isGeneratingPoster = false
            uni.hideLoading()

            // 显示海报弹窗
            this.displayGeneratedPoster(res)
          })
          .catch((err) => {
            console.error('海报生成失败:', err)
            this.isGeneratingPoster = false
            uni.hideLoading()
            this.$api.msg('海报生成失败，请稍后重试')
          })
      } catch (error) {
        console.error('fastposter 初始化失败:', error)
        this.isGeneratingPoster = false
        uni.hideLoading()
        this.$api.msg('海报生成失败')
      }
    },

    // 显示生成的海报
    displayGeneratedPoster(posterUrl) {
      // console.log('海报URL:', posterUrl)
      console.log('生成海报base64',posterUrl&&posterUrl.length)
      // 直接显示海报弹窗，提示文字在弹窗中显示
      this.showPosterModal = true
    },
    
    // 绘制海报画布
    drawPosterCanvas() {
      const ctx = uni.createCanvasContext('posterCanvas', this)
      
      // 设置背景色
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, this.posterCanvasWidth, this.posterCanvasHeight)

      // 绘制标题
      ctx.fillStyle = '#333333'
      ctx.font = 'bold 24px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('金句卡分享', this.posterCanvasWidth / 2, 50)
      
      // 绘制卡片图片
      if (this.drawnCardImage && this.currentRid) {
        uni.getImageInfo({
          src: this.drawnCardImage,
          success: (res) => {
            const imgWidth = 200
            const imgHeight = 280
            const imgX = (this.posterCanvasWidth - imgWidth) / 2
            const imgY = 80

            ctx.drawImage(res.path, imgX, imgY, imgWidth, imgHeight)

            // 绘制卡片编号
            ctx.fillStyle = '#666666'
            ctx.font = '16px sans-serif'
            ctx.fillText(`卡片编号: ${this.currentRid}`, this.posterCanvasWidth / 2, imgY + imgHeight + 30)

            // 绘制底部文字
            ctx.fillStyle = '#999999'
            ctx.font = '14px sans-serif'
            ctx.fillText('扫码体验更多精彩内容', this.posterCanvasWidth / 2, this.posterCanvasHeight - 50)
            
            ctx.draw(false, () => {
              this.isGeneratingPoster = false
              this.showPosterModal = true
              uni.hideLoading()
            })
          },
          fail: () => {
            this.isGeneratingPoster = false
            uni.hideLoading()
            this.$api.msg('生成海报失败')
          }
        })
      } else {
        this.isGeneratingPoster = false
        uni.hideLoading()
        this.$api.msg('请先抽取卡片')
      }
    },
    
    // 保存海报
    savePoster() {
      console.log('开始保存海报，posterImagePath:', this.posterImagePath)

      // #ifdef H5
      // H5环境下直接下载图片
      if (this.posterImagePath) {
        const link = document.createElement('a')
        link.href = this.posterImagePath
        link.download = `海报_${new Date().getTime()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$api.msg('海报下载成功')
        return
      }
      // #endif

      // #ifdef APP-PLUS || MP
      if (this.posterImagePath) {
        // 保存 fastposter 生成的海报
        uni.showLoading({
          title: '保存中...'
        })

        // 检查是否有权限申请API
        const saveToAlbum = (filePath) => {
          uni.saveImageToPhotosAlbum({
            filePath: filePath,
            success: () => {
              uni.hideLoading()
              this.$api.msg('海报已保存到相册')
              console.log('保存成功')
            },
            fail: (err) => {
              uni.hideLoading()
              console.error('保存到相册失败:', err)
              this.$api.msg('保存失败，请检查相册权限')
            }
          })
        }

        uni.downloadFile({
          url: this.posterImagePath,
          success: (res) => {
            console.log('下载成功:', res)
            if (res.statusCode === 200) {
              // 检查是否支持权限申请
              if (typeof uni.authorize === 'function') {
                uni.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => {
                    saveToAlbum(res.tempFilePath)
                  },
                  fail: () => {
                    // 权限申请失败，直接尝试保存
                    saveToAlbum(res.tempFilePath)
                  }
                })
              } else {
                // 不支持权限申请，直接保存
                saveToAlbum(res.tempFilePath)
              }
            } else {
              uni.hideLoading()
              console.error('下载失败，状态码:', res.statusCode)
              this.$api.msg('下载海报失败')
            }
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('下载失败:', err)
            this.$api.msg('下载海报失败')
          }
        })
      } else {
        console.log('没有海报路径，使用canvas保存')
        // 备用方案：保存 canvas 生成的海报
        uni.canvasToTempFilePath({
          canvasId: 'posterCanvas',
          success: (res) => {
            const saveToAlbum = (filePath) => {
              uni.saveImageToPhotosAlbum({
                filePath: filePath,
                success: () => {
                  this.$api.msg('海报已保存到相册')
                },
                fail: (err) => {
                  console.error('Canvas保存失败:', err)
                  this.$api.msg('保存失败，请检查相册权限')
                }
              })
            }

            if (typeof uni.authorize === 'function') {
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => {
                  saveToAlbum(res.tempFilePath)
                },
                fail: () => {
                  saveToAlbum(res.tempFilePath)
                }
              })
            } else {
              saveToAlbum(res.tempFilePath)
            }
          },
          fail: (err) => {
            console.error('Canvas生成失败:', err)
            this.$api.msg('生成图片失败')
          }
        })
      }
      // #endif
    },
    
    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false
    },

    showChatroomPopup(){
      this.activityImageUrl='https://sg-tduck-sh.oss-cn-shanghai.aliyuncs.com/upload/e53e4a4498a4afa490ce44fb7d822c93/a3eb3b28803f4ed086310555afca7840.png'
      this.showActivityModal = true
      console.log('弹窗已设置为显示状态')
    },

    // 显示活动弹窗
    showActivityPopup() {
      console.log('尝试显示活动弹窗，当前状态:', {
        activityImageUrl: this.activityImageUrl,
        showActivityModal: this.showActivityModal,
        cachedResourceId: this.cachedResourceId
      })

      if (this.activityImageUrl) {
        console.log('显示活动弹窗，图片URL:', this.activityImageUrl)
        // 延迟显示弹窗，让用户先看到抽卡结果
        setTimeout(() => {
          this.showActivityModal = true
          console.log('弹窗已设置为显示状态')
        }, 1000)
      } else {
        console.log('没有活动图片，重新检查活动状态并准备显示弹窗')
        // 如果没有活动图片，尝试重新检查活动状态，并在获取到图片后显示弹窗
        this.checkActivityStatus(true) // 传入true表示检查完成后要显示弹窗
      }
    },

    // 关闭活动弹窗
    closeActivityModal() {
      this.showActivityModal = false
      this.activityModalClosed = true // 标记弹窗已被用户关闭
      console.log('关闭活动弹窗，设置activityModalClosed = true')
    },

    // 活动图片加载成功
    onActivityImageLoad(e) {
      console.log('活动图片加载成功:', e)
    },

    // 活动图片加载失败
    onActivityImageError(e) {
      console.error('活动图片加载失败:', e)
      console.error('图片URL:', this.activityImageUrl)
    },

    // 测试弹窗使用简单图片
    testPopupWithSimpleImage() {
      console.log('测试弹窗使用简单图片')
      // 使用项目中已有的图片进行测试
      this.activityImageUrl = 'https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_bg.png'
      this.showActivityModal = true
      console.log('设置测试图片URL:', this.activityImageUrl)
    },
    
    // 抽卡函数
    drawCard() {
      if (!this.hasLogin) {
        this.$refs&&this.$refs.loginPop&&this.$refs.loginPop.openLogin()
        return
      }

      if (this.isDrawing) {
        return
      }

      this.isDrawing = true
      // 重置翻转状态，但保持按钮布局
      this.isFlipping = false
      // 如果当前已展示正面，为了再次触发动画，先临时翻回到背面
      if (this.isCardDrawn) {
        this.isFlippingReset = true
      }
      // 注意：不重置 isCardDrawn，保持三个按钮的布局
      console.log('开始抽卡，当前isCardDrawn:', this.isCardDrawn)

      // 触发爆发粒子效果
      this.createBurstParticles()

      // 准备请求参数 - 抽卡接口不传递lastResourceId
      const requestData = {
        groupKey: this.groupKey
      }

      console.log('抽卡请求，只传递groupKey:', this.groupKey)

      // 该接口支持通过 groupKey 随机获取表单数据
      this.$request({
        url: 'v3/promotion/front/cms/get',
        method: 'GET',
        data: requestData
      }).then(res => {
        if (res.state === 200 && res.data && Array.isArray(res.data) && res.data.length > 0) {
          const resourceData = res.data[0] // 取数组第一个元素
          // 根据新接口的数据结构获取rid，可能在不同字段中
          const rid = resourceData.resourceId || resourceData.rid || resourceData.id || resourceData.formKey
          // 从resourceData中获取卡片图片
          const resourceDataContent = resourceData.resourceData || {}
          const imageUrl = this.findImageUrl(resourceDataContent) || resourceData.imageUrl || resourceData.image || resourceData.url

          if (rid && imageUrl) {
            // 更新rid到URL参数
            this.updateUrlWithRid(rid)

            // 模拟抽卡动画
            setTimeout(() => {
              // 设置抽取到的图片数据
              this.resourceData = resourceData
              this.drawnCardImage = imageUrl
              this.currentRid = rid

              // 如果之前做了临时复位，把复位状态取消，准备执行真实翻转
              this.isFlippingReset = false
              // 每次翻到正面前重置并触发镭射扫光
              // 切换一次方案，借助不同动画名强制重播
              this.holoAlt = !this.holoAlt
              this.holoSweep = false
              this.$nextTick(() => { this.holoSweep = true })
              // 开始翻转动画
              this.isFlipping = true

              // 翻转动画完成后显示正面
              setTimeout(() => {
                this.isCardDrawn = true
                this.isFlipping = false
                this.isDrawing = false
                // 动画结束收尾，稍后关闭holoSweep避免常驻
                setTimeout(() => { this.holoSweep = false }, 300)
                console.log('抽卡完成，设置isCardDrawn = true')
                this.$api.msg('恭喜获得金句卡！')
              }, 400) // 翻转动画时间
            }, 800)

            console.log('成功抽取卡片:', { rid, imageUrl })
          } else {
            console.warn('CMS接口返回的数据缺少必要字段:', { rid, imageUrl, data: resourceData })
            this.handleDrawCardFallback(false) // 不自动显示弹窗
          }
        } else {
          // 检查是否是成功状态但无数据的情况
          if (res.state === 200 && Array.isArray(res.data) && res.data.length === 0) {
            console.log('抽卡成功，但无新活动数据，不显示弹窗')
            // 清除活动图片URL
            this.activityImageUrl = ''
            // 不显示弹窗，直接使用模拟数据
            this.handleDrawCardFallback(false) // 传入false表示不显示弹窗
          } else {
            console.log('CMS接口返回异常:', res)
            console.log('其他异常情况，使用模拟数据')
            this.handleDrawCardFallback(false) // 不自动显示弹窗
          }
        }
      }).catch(err => {
        console.error('CMS抽卡接口调用失败，使用模拟数据:', err)
        this.handleDrawCardFallback(false) // 不自动显示弹窗
      })
    },

    // 抽卡失败时的备用处理
    handleDrawCardFallback(showPopup = false) {
      // 生成500-600之间的随机数作为演示
      const cardNumber = Math.floor(Math.random() * 100) + 500

      // 更新rid到URL参数
      this.updateUrlWithRid(cardNumber)

      // 模拟抽卡动画
      setTimeout(() => {
        // 设置抽取到的图片数据
        this.drawnCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${cardNumber}.webp`
        this.currentRid = cardNumber

        // 如果之前做了临时复位，把复位状态取消，准备执行真实翻转
        this.isFlippingReset = false
        // 每次翻到正面前重置并触发镭射扫光
        // 切换一次方案，借助不同动画名强制重播
        this.holoAlt = !this.holoAlt
        this.holoSweep = false
        this.$nextTick(() => { this.holoSweep = true })
        // 开始翻转动画
        this.isFlipping = true

        // 翻转动画完成后显示正面
        setTimeout(() => {
          this.isCardDrawn = true
          this.isFlipping = false
          this.isDrawing = false
          // 动画结束收尾，稍后关闭holoSweep避免常驻
          setTimeout(() => { this.holoSweep = false }, 300)
          console.log('备用抽卡完成，设置isCardDrawn = true')
          this.$api.msg('恭喜获得金句卡！')

          // 如果需要显示弹窗
          if (showPopup) {
            this.showActivityPopup()
          }
        }, 400) // 翻转动画时间
      }, 800)
    },
    
    // 更新URL参数
    updateUrlWithRid(rid) {
      // #ifdef H5
      const currentUrl = new URL(window.location.href)
      currentUrl.searchParams.set('rid', rid)
      window.history.replaceState({}, '', currentUrl.toString())
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序端可以考虑使用其他方式保存状态，比如本地存储
      uni.setStorageSync('current_card_rid', rid)
      // #endif
    },
    
    // 跳转商品链接
    goToProduct() {
      // #ifdef H5
      window.open('https://baidu.com', '_blank')
      // #endif
      // #ifndef H5
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent('https://baidu.com')}`
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.camp-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .bg-image {
    width: 100%;
    height: 100%;
  }
}

.particle-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  /* 确保Canvas覆盖整个屏幕包括安全区域 */
  min-height: 100vh;
  z-index: 1;
  pointer-events: none;
}

.container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 224rpx 20rpx 200rpx;
}

.cards-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 800rpx;
  margin-bottom: 128rpx;
  perspective: 1000rpx;
  /* 创建新的层叠上下文，确保iOS 16兼容性 */
  isolation: isolate;
}

.side-card {
  position: absolute;
  width: 320rpx;
  height: 500rpx;
  z-index: 1;
  animation: cardFloat 3s ease-in-out infinite;
  /* iOS 16兼容性优化 */
  will-change: transform;
  backface-visibility: hidden;

  &.left-card {
    left: 20rpx;
    --rotate-y: -15deg;
    --rotate-z: -5deg;
    /* 使用translateZ强制硬件加速，确保层级正确 */
    transform: rotateY(-15deg) rotateZ(-5deg) translateZ(0);
    animation-delay: 0s;
  }

  &.right-card {
    right: 20rpx;
    --rotate-y: 15deg;
    --rotate-z: 5deg;
    /* 使用translateZ强制硬件加速，确保层级正确 */
    transform: rotateY(15deg) rotateZ(5deg) translateZ(0);
    animation-delay: 1.5s;
  }

  .side-card-image {
    width: 100%;
    height: 100%;
    // border-radius: 16rpx;
    // box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.4);
  }
}

.main-card {
  position: relative;
  /* 大幅提高z-index确保始终在最上层 */
  z-index: 10;
  width: 500rpx;
  height: 884rpx;
  transition: transform 0.8s ease-in-out;
  transform-style: preserve-3d;
  /* iOS 16兼容性优化 */
  will-change: transform;
  /* 子面板设置 backface-visibility，父容器不需要，避免翻转后整体被隐藏（微信内核兼容） */
  /* 强制创建合成层，确保层级正确 */
  transform: translateZ(1px);
  -webkit-transform-style: preserve-3d;

  &.card-flip {
    /* 翻转时也保持translateZ，确保层级 */
    transform: rotateY(180deg) translateZ(1px);
  }

  /* 二次抽卡时临时把卡片翻回背面以触发再次翻转（不改变按钮布局） */
  &.card-flip-reset {
    transform: rotateY(0deg) translateZ(1px);
  }

  .card-back,
  .card-front {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }

  .card-back {
    .main-card-image {
      width: 120%;
      height: 120%;
      margin: -10%;
      border-radius: 0;
      box-shadow: none;
      border: none;
    }
  }

  .card-front {
    /* 镭射光效果容器 */
    position: relative;
    overflow: hidden;

    .main-card-image {
      width: 100%;
      height: 100%;
      border-radius: 32rpx;
      box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.5);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      position: relative;
      z-index: 1;
    }

    /* 镭射光渐变层 */
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      /* 更强烈的彩虹镭射渐变：多色、较高亮度，覆盖范围更大 */
      background: linear-gradient(
        115deg,
        rgba(255, 0, 132, 0) 0%,
        rgba(255, 0, 132, 0.55) 12%,
        rgba(252, 164, 0, 0.55) 24%,
        rgba(255, 255, 0, 0.55) 36%,
        rgba(0, 255, 138, 0.55) 48%,
        rgba(0, 207, 255, 0.55) 60%,
        rgba(204, 76, 250, 0.55) 72%,
        rgba(255, 255, 255, 0.85) 84%,
        rgba(255, 255, 255, 0) 100%
      );
      background-size: 350% 350%;
      background-position: -130% -130%;
      mix-blend-mode: color-dodge;
      filter: saturate(1.25) brightness(1.05) contrast(1.15);
      opacity: 0;
      z-index: 2;
      border-radius: 32rpx;
      transition: all 0.6s ease;
    }

    /* 闪光粒子层 */
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      /* 更清晰的彩虹闪光与粒子（提高亮度、饱和度与点密度） */
      background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.95) 1.5px, transparent 1.6px),
        radial-gradient(circle at 80% 30%, rgba(255, 240, 180, 0.8) 1.3px, transparent 1.5px),
        radial-gradient(circle at 30% 80%, rgba(170, 240, 255, 0.7) 1.3px, transparent 1.5px),
        radial-gradient(circle at 70% 60%, rgba(255, 200, 255, 0.7) 1.2px, transparent 1.4px),
        linear-gradient(125deg,
          rgba(255, 0, 132, 0.45) 12%,
          rgba(252, 164, 0, 0.40) 28%,
          rgba(255, 255, 0, 0.40) 40%,
          rgba(0, 255, 138, 0.30) 55%,
          rgba(0, 207, 255, 0.40) 68%,
          rgba(204, 76, 250, 0.45) 83%
        );
      background-size: 220% 220%;
      background-position: -110% -110%;
      mix-blend-mode: color-dodge;
      filter: saturate(1.35) brightness(1.05) contrast(1.1);
      opacity: 0;
      z-index: 3;
      border-radius: 32rpx;
      transition: all 0.8s ease;
    }
  }

  .card-back {
    transform: rotateY(0deg) translateZ(0.1px);
  }

  .card-front {
    transform: rotateY(180deg) translateZ(0.1px);
  }
}

.draw-button-container {
  position: relative;
  display: flex;
  justify-content: center;
  // margin-bottom: 12rpx;

  .draw-button {
    width: 360rpx;
    height: auto;
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));

    &:active:not(.disabled) {
      opacity: 0.7;
      transition: all 0.1s ease-out;
    }

    &.disabled {
      opacity: 0.7;
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.2));
    }
  }

  .button-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 36rpx;
    font-weight: bold;
    /* 外描边效果 */
    text-shadow:
      -2rpx -2rpx 0 #EE7C31,
      2rpx -2rpx 0 #EE7C31,
      -2rpx 2rpx 0 #EE7C31,
      2rpx 2rpx 0 #EE7C31,
      0 4rpx 8rpx #ED7330;
    pointer-events: none;
    z-index: 10;
  }
}

/* 抽卡成功后的三个按钮容器 */
.action-buttons-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.action-button-item {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-button-item:first-child {
  flex: 2; /* 第一个按钮占更多空间 */
}

.action-button-item:not(:first-child) {
  flex: 0 0 auto; /* 后两个按钮固定尺寸 */
}

.action-button {
  width: 100%;
  max-width: 200rpx;
  height: auto;
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));

  &:active:not(.disabled) {
    opacity: 0.7;
    transition: all 0.1s ease-out;
  }

  &.disabled {
    opacity: 0.7;
    filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.2));
  }
}

/* 第一个按钮特殊样式 */
.action-button-item:first-child .action-button {
  max-width: 360rpx; /* 与first-button宽度保持一致 */
  height: 132rpx;
}

/* 第一个按钮背景图片 */
.first-button {
  background-image: url('https://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_button.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 360rpx; /* 增加宽度避免文字换行 */
  height: 132rpx;
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:active:not(.disabled) {
    opacity: 0.7;
    transition: all 0.1s ease-out;
  }

  &.disabled {
    opacity: 0.7;
  }
}

/* 后两个按钮样式 */
.action-button-item:not(:first-child) .action-button {
  width: 128rpx;
  height: 128rpx;
  max-width: 128rpx;

  &:active:not(.disabled) {
    opacity: 0.7;
    transition: all 0.1s ease-out;
  }

  &.disabled {
    opacity: 0.7;
    filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.2));
  }
}

.action-button-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 28rpx; /* 稍微减小字体避免换行 */
  font-weight: bold;
  white-space: nowrap; /* 防止文字换行 */
  /* 外描边效果 */
  text-shadow:
    -2rpx -2rpx 0 #EE7C31,
    2rpx -2rpx 0 #EE7C31,
    -2rpx 2rpx 0 #EE7C31,
    2rpx 2rpx 0 #EE7C31,
    0 4rpx 8rpx #ED7330;
  pointer-events: none;
  z-index: 10;
}

// .banner-container {
//   position: fixed;
//   bottom: 60rpx;
//   left: 40rpx;
//   right: 40rpx;
//   height: 152rpx;
//   background-image: url('https://sg-tduck-sh.oss-cn-shanghai.aliyuncs.com/jjk/imgs/banner-01.png');
//   background-size: cover;
//   background-position: center;
//   background-repeat: no-repeat;
//   border-radius: 20rpx;
//   z-index: 10;
// }

/* 活动弹窗样式 */
.activity-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  animation: fadeIn 0.3s ease-in-out;
}

.activity-modal-content {
  position: relative;
  width: 90%; /* 使用更大的宽度 */
  max-width: 700rpx; /* 增加最大宽度 */
  max-height: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.activity-image {
  width: 100%; /* 使用容器的全部宽度 */
  max-width: 700rpx; /* 增加最大宽度 */
  height: auto;
  min-height: 800rpx; /* 增加最小高度 */
  border-radius: 30rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.5);
  animation: scaleIn 0.4s ease-out;
}

.activity-placeholder {
  width: 100%;
  max-width: 700rpx;
  height: 500rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  color: white;
  font-size: 32rpx;
}

.activity-close-btn {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.activity-close-btn:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.8);
}

/* 新的关闭按钮样式 - 位于弹窗下方 */
.activity-close-btn-bottom {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  margin-top: 20rpx;
}

.activity-close-btn-bottom:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.8);
}

/* 海报展示样式 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 80rpx; /* 四周留40px */
}

.poster-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.poster-image {
  width: 100%;
  height: calc(100% - 120rpx); /* 设置固定高度而不是auto */
  max-height: calc(100% - 120rpx); /* 为提示文字和关闭按钮留空间 */
  // border-radius: 48rpx;
  // box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
  flex: 1;
  object-fit: cover; /* 改为cover确保填满容器 */
}

.poster-canvas {
  width: 100%;
  height: auto;
  max-height: calc(100% - 120rpx); /* 为提示文字和关闭按钮留空间 */
  border-radius: 20rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
  flex: 1;
}

/* 海报提示文字 */
.poster-tip {
  color: white;
  font-size: 28rpx;
  text-align: center;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  flex-shrink: 0; /* 不压缩 */
}

/* 海报关闭按钮 */
.poster-close-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 0; /* 不压缩 */
}

.poster-close-btn:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.8);
}

/* 动画效果 */
@keyframes cardFloat {
  0%, 100% {
    /* 动画中也保持translateZ，确保层级稳定 */
    transform: translateY(0rpx) rotateY(var(--rotate-y, 0deg)) rotateZ(var(--rotate-z, 0deg)) translateZ(0);
  }
  50% {
    /* 动画中也保持translateZ，确保层级稳定 */
    transform: translateY(-20rpx) rotateY(var(--rotate-y, 0deg)) rotateZ(var(--rotate-z, 0deg)) translateZ(0);
  }
}



@keyframes buttonGlow {
  0%, 100% {
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));
  }
  50% {
    filter: drop-shadow(0 15rpx 30rpx rgba(255, 215, 0, 0.4));
  }
}

.draw-button {
  animation: buttonGlow 2s ease-in-out infinite;
}

/* 弹窗动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
/* 翻到正面时触发镭射光扫过动画 */
.main-card.holo-sweep .card-front::before,
.main-card.holo-sweep .card-front::after,
.main-card.holo-sweep.holo-alt .card-front::before,
.main-card.holo-sweep.holo-alt .card-front::after {
  opacity: 1;
}

/* 方案A：默认动画名 */
.main-card.holo-sweep:not(.holo-alt) .card-front::before {
  animation: jjkHoloGradient 0.9s ease-out forwards;
}
.main-card.holo-sweep:not(.holo-alt) .card-front::after {
  animation: jjkHoloSparkle 1.2s ease-out forwards;
}

/* 方案B：切换动画名，保证每次触发都能重新播放 */
.main-card.holo-sweep.holo-alt .card-front::before {
  animation: jjkHoloGradientB 0.9s ease-out forwards;
}
.main-card.holo-sweep.holo-alt .card-front::after {
  animation: jjkHoloSparkleB 1.2s ease-out forwards;
}

@keyframes jjkHoloGradient {
  0%   { background-position: -150% -150%; opacity: 0.0; filter: saturate(1) brightness(1) contrast(1); }
  12%  { opacity: 0.65; }
  50%  { opacity: 1.0; filter: saturate(1.2) brightness(1.05) contrast(1.1); }
  100% { background-position: 150% 150%; opacity: 0; filter: saturate(1) brightness(1) contrast(1); }
}

@keyframes jjkHoloSparkle {
  0%   { background-position: -110% -110%; opacity: 0.0; filter: brightness(0.95) contrast(1.0) saturate(1.05); }
  25%  { opacity: 0.8; }
  55%  { opacity: 1.0; filter: brightness(1.15) contrast(1.2) saturate(1.2); }
  100% { background-position: 110% 110%; opacity: 0; filter: brightness(1.0) contrast(1.0) saturate(1.0); }
}

</style>
